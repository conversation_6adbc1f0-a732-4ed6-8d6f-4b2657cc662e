{"version": 3, "sources": [], "sections": [{"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Axmadcodes%20media%20pc/news%20collection/crypto-dashboard/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { TrendingUp, TrendingDown, Activity } from 'lucide-react';\n\ninterface Blockchain {\n  id: string;\n  name: string;\n  symbol: string;\n  market_cap: number | null;\n  volume_24h: number | null;\n  price_change_24h: number | null;\n}\n\nexport default function Home() {\n  const [blockchains, setBlockchains] = useState<Blockchain[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchBlockchains = async () => {\n      try {\n        const response = await fetch('http://localhost:8000/blockchains');\n        if (!response.ok) {\n          throw new Error('Failed to fetch blockchains');\n        }\n        const data = await response.json();\n        setBlockchains(data);\n      } catch (err) {\n        setError(err instanceof Error ? err.message : 'An error occurred');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchBlockchains();\n  }, []);\n\n  const formatNumber = (num: number | null) => {\n    if (num === null) return 'N/A';\n    if (num >= 1e12) return `$${(num / 1e12).toFixed(2)}T`;\n    if (num >= 1e9) return `$${(num / 1e9).toFixed(2)}B`;\n    if (num >= 1e6) return `$${(num / 1e6).toFixed(2)}M`;\n    if (num >= 1e3) return `$${(num / 1e3).toFixed(2)}K`;\n    return `$${num.toFixed(2)}`;\n  };\n\n  const formatPercentage = (num: number | null) => {\n    if (num === null) return 'N/A';\n    return `${num.toFixed(2)}%`;\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <Activity className=\"w-8 h-8 animate-spin mx-auto mb-4 text-blue-600\" />\n          <p className=\"text-gray-600\">Loading blockchains...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <p className=\"text-red-600 mb-4\">Error: {error}</p>\n          <button\n            onClick={() => window.location.reload()}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\"\n          >\n            Retry\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-2\">\n            Crypto Intelligence Dashboard\n          </h1>\n          <p className=\"text-gray-600\">\n            Explore blockchain ecosystems and their top assets\n          </p>\n        </div>\n\n        <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\n          {blockchains.map((blockchain) => (\n            <Link\n              key={blockchain.id}\n              href={`/blockchain/${blockchain.id}`}\n              className=\"block\"\n            >\n              <div className=\"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 border border-gray-200 hover:border-blue-300\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div>\n                    <h3 className=\"text-xl font-semibold text-gray-900\">\n                      {blockchain.name}\n                    </h3>\n                    <p className=\"text-gray-500 text-sm\">{blockchain.symbol}</p>\n                  </div>\n                  <div className=\"text-right\">\n                    {blockchain.price_change_24h !== null && (\n                      <div className={`flex items-center ${\n                        blockchain.price_change_24h >= 0 ? 'text-green-600' : 'text-red-600'\n                      }`}>\n                        {blockchain.price_change_24h >= 0 ? (\n                          <TrendingUp className=\"w-4 h-4 mr-1\" />\n                        ) : (\n                          <TrendingDown className=\"w-4 h-4 mr-1\" />\n                        )}\n                        <span className=\"text-sm font-medium\">\n                          {formatPercentage(blockchain.price_change_24h)}\n                        </span>\n                      </div>\n                    )}\n                  </div>\n                </div>\n\n                <div className=\"space-y-2\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-600 text-sm\">Market Cap:</span>\n                    <span className=\"font-medium text-sm\">\n                      {formatNumber(blockchain.market_cap)}\n                    </span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-600 text-sm\">24h Volume:</span>\n                    <span className=\"font-medium text-sm\">\n                      {formatNumber(blockchain.volume_24h)}\n                    </span>\n                  </div>\n                </div>\n\n                <div className=\"mt-4 pt-4 border-t border-gray-100\">\n                  <span className=\"text-blue-600 text-sm font-medium hover:text-blue-700\">\n                    View Assets →\n                  </span>\n                </div>\n              </div>\n            </Link>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;AAJA;;;;;AAee,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,qRAAQ,EAAe,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,qRAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,qRAAQ,EAAgB;IAElD,IAAA,sRAAS,EAAC;QACR,MAAM,mBAAmB;YACvB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBACA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,eAAe;YACjB,EAAE,OAAO,KAAK;gBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAChD,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,OAAO,MAAM,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACtD,IAAI,OAAO,KAAK,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACpD,IAAI,OAAO,KAAK,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACpD,IAAI,OAAO,KAAK,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACpD,OAAO,CAAC,CAAC,EAAE,IAAI,OAAO,CAAC,IAAI;IAC7B;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,QAAQ,MAAM,OAAO;QACzB,OAAO,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC;IAC7B;IAEA,IAAI,SAAS;QACX,qBACE,kTAAC;YAAI,WAAU;sBACb,cAAA,kTAAC;gBAAI,WAAU;;kCACb,kTAAC,0RAAQ;wBAAC,WAAU;;;;;;kCACpB,kTAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,OAAO;QACT,qBACE,kTAAC;YAAI,WAAU;sBACb,cAAA,kTAAC;gBAAI,WAAU;;kCACb,kTAAC;wBAAE,WAAU;;4BAAoB;4BAAQ;;;;;;;kCACzC,kTAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,kTAAC;QAAI,WAAU;kBACb,cAAA,kTAAC;YAAI,WAAU;;8BACb,kTAAC;oBAAI,WAAU;;sCACb,kTAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,kTAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAK/B,kTAAC;oBAAI,WAAU;8BACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,kTAAC,2OAAI;4BAEH,MAAM,CAAC,YAAY,EAAE,WAAW,EAAE,EAAE;4BACpC,WAAU;sCAEV,cAAA,kTAAC;gCAAI,WAAU;;kDACb,kTAAC;wCAAI,WAAU;;0DACb,kTAAC;;kEACC,kTAAC;wDAAG,WAAU;kEACX,WAAW,IAAI;;;;;;kEAElB,kTAAC;wDAAE,WAAU;kEAAyB,WAAW,MAAM;;;;;;;;;;;;0DAEzD,kTAAC;gDAAI,WAAU;0DACZ,WAAW,gBAAgB,KAAK,sBAC/B,kTAAC;oDAAI,WAAW,CAAC,kBAAkB,EACjC,WAAW,gBAAgB,IAAI,IAAI,mBAAmB,gBACtD;;wDACC,WAAW,gBAAgB,IAAI,kBAC9B,kTAAC,oSAAU;4DAAC,WAAU;;;;;iFAEtB,kTAAC,0SAAY;4DAAC,WAAU;;;;;;sEAE1B,kTAAC;4DAAK,WAAU;sEACb,iBAAiB,WAAW,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;kDAOvD,kTAAC;wCAAI,WAAU;;0DACb,kTAAC;gDAAI,WAAU;;kEACb,kTAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,kTAAC;wDAAK,WAAU;kEACb,aAAa,WAAW,UAAU;;;;;;;;;;;;0DAGvC,kTAAC;gDAAI,WAAU;;kEACb,kTAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,kTAAC;wDAAK,WAAU;kEACb,aAAa,WAAW,UAAU;;;;;;;;;;;;;;;;;;kDAKzC,kTAAC;wCAAI,WAAU;kDACb,cAAA,kTAAC;4CAAK,WAAU;sDAAwD;;;;;;;;;;;;;;;;;2BA9CvE,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;AAyDhC", "debugId": null}}]}