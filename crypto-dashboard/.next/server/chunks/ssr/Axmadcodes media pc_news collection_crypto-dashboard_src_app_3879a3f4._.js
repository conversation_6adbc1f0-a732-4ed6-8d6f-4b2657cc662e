module.exports = [
"[project]/Axmadcodes media pc/news collection/crypto-dashboard/src/app/favicon.ico (static in ecmascript)", ((__turbopack_context__) => {

__turbopack_context__.v("/_next/static/media/favicon.0b3bf435.ico");}),
"[project]/Axmadcodes media pc/news collection/crypto-dashboard/src/app/favicon.ico.mjs { IMAGE => \"[project]/Axmadcodes media pc/news collection/crypto-dashboard/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$Axmadcodes__media__pc$2f$news__collection$2f$crypto$2d$dashboard$2f$src$2f$app$2f$favicon$2e$ico__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/Axmadcodes media pc/news collection/crypto-dashboard/src/app/favicon.ico (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$Axmadcodes__media__pc$2f$news__collection$2f$crypto$2d$dashboard$2f$src$2f$app$2f$favicon$2e$ico__$28$static__in__ecmascript$29$__["default"],
    width: 256,
    height: 256
};
}),
];

//# sourceMappingURL=Axmadcodes%20media%20pc_news%20collection_crypto-dashboard_src_app_3879a3f4._.js.map