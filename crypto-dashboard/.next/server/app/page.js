var R=require("../chunks/ssr/[turbopack]_runtime.js")("server/app/page.js")
R.c("server/chunks/ssr/eec7e_4a5aa446._.js")
R.c("server/chunks/ssr/[root-of-the-server]__e8a2741f._.js")
R.c("server/chunks/ssr/Axmadcodes media pc_news collection_crypto-dashboard_src_app_3879a3f4._.js")
R.c("server/chunks/ssr/[root-of-the-server]__1546a406._.js")
R.c("server/chunks/ssr/eec7e_next_dist_client_components_628743be._.js")
R.c("server/chunks/ssr/eec7e_next_dist_client_components_builtin_forbidden_39448c7d.js")
R.c("server/chunks/ssr/eec7e_next_dist_client_components_builtin_unauthorized_4e6d32e2.js")
R.c("server/chunks/ssr/eec7e_next_dist_client_components_builtin_global-error_ab915a96.js")
R.c("server/chunks/ssr/eec7e_next_dist_599afc9a._.js")
R.c("server/chunks/ssr/[root-of-the-server]__b50e7716._.js")
R.m("[project]/Axmadcodes media pc/news collection/crypto-dashboard/.next-internal/server/app/page/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/Axmadcodes media pc/news collection/crypto-dashboard/node_modules/next/dist/esm/build/templates/app-page.js?page=/page { GLOBAL_ERROR_MODULE => \"[project]/Axmadcodes media pc/news collection/crypto-dashboard/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/Axmadcodes media pc/news collection/crypto-dashboard/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/Axmadcodes media pc/news collection/crypto-dashboard/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/Axmadcodes media pc/news collection/crypto-dashboard/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/Axmadcodes media pc/news collection/crypto-dashboard/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/Axmadcodes media pc/news collection/crypto-dashboard/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/Axmadcodes media pc/news collection/crypto-dashboard/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/Axmadcodes media pc/news collection/crypto-dashboard/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/Axmadcodes media pc/news collection/crypto-dashboard/src/app/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)")
module.exports=R.m("[project]/Axmadcodes media pc/news collection/crypto-dashboard/node_modules/next/dist/esm/build/templates/app-page.js?page=/page { GLOBAL_ERROR_MODULE => \"[project]/Axmadcodes media pc/news collection/crypto-dashboard/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/Axmadcodes media pc/news collection/crypto-dashboard/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/Axmadcodes media pc/news collection/crypto-dashboard/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/Axmadcodes media pc/news collection/crypto-dashboard/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/Axmadcodes media pc/news collection/crypto-dashboard/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/Axmadcodes media pc/news collection/crypto-dashboard/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/Axmadcodes media pc/news collection/crypto-dashboard/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/Axmadcodes media pc/news collection/crypto-dashboard/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/Axmadcodes media pc/news collection/crypto-dashboard/src/app/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)").exports
