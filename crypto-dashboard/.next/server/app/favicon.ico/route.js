var R=require("../../chunks/[turbopack]_runtime.js")("server/app/favicon.ico/route.js")
R.c("server/chunks/eec7e_next_f7122e2b._.js")
R.c("server/chunks/[root-of-the-server]__6b40f481._.js")
R.m("[project]/Axmadcodes media pc/news collection/crypto-dashboard/.next-internal/server/app/favicon.ico/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/Axmadcodes media pc/news collection/crypto-dashboard/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/Axmadcodes media pc/news collection/crypto-dashboard/src/app/favicon--route-entry.js [app-rsc] (ecmascript)\" } [app-rsc] (ecmascript)")
module.exports=R.m("[project]/Axmadcodes media pc/news collection/crypto-dashboard/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/Axmadcodes media pc/news collection/crypto-dashboard/src/app/favicon--route-entry.js [app-rsc] (ecmascript)\" } [app-rsc] (ecmascript)").exports
