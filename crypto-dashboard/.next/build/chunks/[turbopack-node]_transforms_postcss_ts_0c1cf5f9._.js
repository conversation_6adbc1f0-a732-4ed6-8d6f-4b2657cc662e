module.exports = [
"[turbopack-node]/transforms/postcss.ts { CONFIG => \"[project]/Axmadcodes media pc/news collection/crypto-dashboard/postcss.config.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "build/chunks/eec7e_06077cd0._.js",
  "build/chunks/[root-of-the-server]__7bb3e188._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[turbopack-node]/transforms/postcss.ts { CONFIG => \"[project]/Axmadcodes media pc/news collection/crypto-dashboard/postcss.config.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript)");
    });
});
}),
];