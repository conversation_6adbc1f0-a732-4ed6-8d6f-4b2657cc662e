{"version": 3, "sources": ["../../../src/next-devtools/server/shared.ts"], "sourcesContent": ["import { codeFrameColumns } from 'next/dist/compiled/babel/code-frame'\nimport isInternal from '../../shared/lib/is-internal'\nimport type { StackFrame } from '../../server/lib/parse-stack'\nimport { ignoreListAnonymousStackFramesIfSandwiched as ignoreListAnonymousStackFramesIfSandwichedGeneric } from '../../server/lib/source-maps'\n\nexport type { StackFrame }\n\nexport interface IgnorableStackFrame extends StackFrame {\n  ignored: boolean\n}\n\nexport interface OriginalStackFramesRequest {\n  frames: readonly StackFrame[]\n  isServer: boolean\n  isEdgeServer: boolean\n  isAppDirectory: boolean\n}\n\nexport type OriginalStackFramesResponse = OriginalStackFrameResponseResult[]\n\nexport type OriginalStackFrameResponseResult =\n  PromiseSettledResult<OriginalStackFrameResponse>\n\nexport interface OriginalStackFrameResponse {\n  originalStackFrame: (StackFrame & { ignored: boolean }) | null\n  originalCodeFrame: string | null\n}\n\nexport function ignoreListAnonymousStackFramesIfSandwiched(\n  responses: OriginalStackFramesResponse\n): void {\n  ignoreListAnonymousStackFramesIfSandwichedGeneric(\n    responses,\n    (response) => {\n      return (\n        response.status === 'fulfilled' &&\n        response.value.originalStackFrame !== null &&\n        response.value.originalStackFrame.file === '<anonymous>'\n      )\n    },\n    (response) => {\n      return (\n        response.status === 'fulfilled' &&\n        response.value.originalStackFrame !== null &&\n        response.value.originalStackFrame.ignored === true\n      )\n    },\n    (response) => {\n      return response.status === 'fulfilled' &&\n        response.value.originalStackFrame !== null\n        ? response.value.originalStackFrame.methodName\n        : ''\n    },\n    (response) => {\n      ;(\n        response as PromiseFulfilledResult<OriginalStackFrameResponse>\n      ).value.originalStackFrame!.ignored = true\n    }\n  )\n}\n\n/**\n * It looks up the code frame of the traced source.\n * @note It ignores Next.js/React internals, as these can often be huge bundled files.\n */\nexport function getOriginalCodeFrame(\n  frame: IgnorableStackFrame,\n  source: string | null,\n  colors: boolean = process.stdout.isTTY\n): string | null {\n  if (!source || isInternal(frame.file)) {\n    return null\n  }\n\n  return codeFrameColumns(\n    source,\n    {\n      start: {\n        // 1-based, but -1 means start line without highlighting\n        line: frame.line1 ?? -1,\n        // 1-based, but 0 means whole line without column highlighting\n        column: frame.column1 ?? 0,\n      },\n    },\n    { forceColor: colors }\n  )\n}\n"], "names": ["getOriginalCodeFrame", "ignoreListAnonymousStackFramesIfSandwiched", "responses", "ignoreListAnonymousStackFramesIfSandwichedGeneric", "response", "status", "value", "originalStackFrame", "file", "ignored", "methodName", "frame", "source", "colors", "process", "stdout", "isTTY", "isInternal", "codeFrameColumns", "start", "line", "line1", "column", "column1", "forceColor"], "mappings": ";;;;;;;;;;;;;;;IAiEgBA,oBAAoB;eAApBA;;IArCAC,0CAA0C;eAA1CA;;;;2BA5BiB;qEACV;4BAEyF;AAyBzG,SAASA,2CACdC,SAAsC;IAEtCC,IAAAA,sDAAiD,EAC/CD,WACA,CAACE;QACC,OACEA,SAASC,MAAM,KAAK,eACpBD,SAASE,KAAK,CAACC,kBAAkB,KAAK,QACtCH,SAASE,KAAK,CAACC,kBAAkB,CAACC,IAAI,KAAK;IAE/C,GACA,CAACJ;QACC,OACEA,SAASC,MAAM,KAAK,eACpBD,SAASE,KAAK,CAACC,kBAAkB,KAAK,QACtCH,SAASE,KAAK,CAACC,kBAAkB,CAACE,OAAO,KAAK;IAElD,GACA,CAACL;QACC,OAAOA,SAASC,MAAM,KAAK,eACzBD,SAASE,KAAK,CAACC,kBAAkB,KAAK,OACpCH,SAASE,KAAK,CAACC,kBAAkB,CAACG,UAAU,GAC5C;IACN,GACA,CAACN;;QAEGA,SACAE,KAAK,CAACC,kBAAkB,CAAEE,OAAO,GAAG;IACxC;AAEJ;AAMO,SAAST,qBACdW,KAA0B,EAC1BC,MAAqB,EACrBC,MAAsC;IAAtCA,IAAAA,mBAAAA,SAAkBC,QAAQC,MAAM,CAACC,KAAK;IAEtC,IAAI,CAACJ,UAAUK,IAAAA,mBAAU,EAACN,MAAMH,IAAI,GAAG;QACrC,OAAO;IACT;QAOYG,cAEEA;IAPd,OAAOO,IAAAA,2BAAgB,EACrBN,QACA;QACEO,OAAO;YACL,wDAAwD;YACxDC,MAAMT,CAAAA,eAAAA,MAAMU,KAAK,YAAXV,eAAe,CAAC;YACtB,8DAA8D;YAC9DW,QAAQX,CAAAA,iBAAAA,MAAMY,OAAO,YAAbZ,iBAAiB;QAC3B;IACF,GACA;QAAEa,YAAYX;IAAO;AAEzB", "ignoreList": [0]}