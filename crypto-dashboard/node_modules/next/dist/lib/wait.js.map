{"version": 3, "sources": ["../../src/lib/wait.ts"], "sourcesContent": ["/**\n * Wait for a given number of milliseconds and then resolve.\n *\n * @param ms the number of milliseconds to wait\n */\nexport async function wait(ms: number) {\n  return new Promise((resolve) => setTimeout(resolve, ms))\n}\n"], "names": ["wait", "ms", "Promise", "resolve", "setTimeout"], "mappings": "AAAA;;;;CAIC;;;;+BACqBA;;;eAAAA;;;AAAf,eAAeA,KAAKC,EAAU;IACnC,OAAO,IAAIC,QAAQ,CAACC,UAAYC,WAAWD,SAASF;AACtD", "ignoreList": [0]}