@import "tailwindcss";

:root {
  --background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --background-light: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --background-dark: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --foreground: #171717;
  --card-background: rgba(255, 255, 255, 0.95);
  --card-border: rgba(255, 255, 255, 0.2);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    --foreground: #ededed;
    --card-background: rgba(45, 55, 72, 0.95);
    --card-border: rgba(255, 255, 255, 0.1);
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  min-height: 100vh;
}

/* Custom gradient backgrounds */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-bg-alt {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-bg-blue {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gradient-bg-purple {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

/* Glass morphism effect */
.glass-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

@media (prefers-color-scheme: dark) {
  .glass-card {
    background: rgba(45, 55, 72, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* Search input styling */
.search-input {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.search-input:focus {
  background: rgba(255, 255, 255, 1);
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

@media (prefers-color-scheme: dark) {
  .search-input {
    background: rgba(45, 55, 72, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: #ededed;
  }

  .search-input:focus {
    background: rgba(45, 55, 72, 1);
    border-color: #4facfe;
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
  }
}
