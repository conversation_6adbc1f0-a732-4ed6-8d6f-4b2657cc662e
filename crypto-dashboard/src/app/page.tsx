'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { TrendingUp, TrendingDown, Activity } from 'lucide-react';

interface Blockchain {
  id: string;
  name: string;
  symbol: string;
  market_cap: number | null;
  volume_24h: number | null;
  price_change_24h: number | null;
}

export default function Home() {
  const [blockchains, setBlockchains] = useState<Blockchain[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBlockchains = async () => {
      try {
        const response = await fetch('http://localhost:8000/blockchains');
        if (!response.ok) {
          throw new Error('Failed to fetch blockchains');
        }
        const data = await response.json();
        setBlockchains(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchBlockchains();
  }, []);

  const formatNumber = (num: number | null) => {
    if (num === null) return 'N/A';
    if (num >= 1e12) return `$${(num / 1e12).toFixed(2)}T`;
    if (num >= 1e9) return `$${(num / 1e9).toFixed(2)}B`;
    if (num >= 1e6) return `$${(num / 1e6).toFixed(2)}M`;
    if (num >= 1e3) return `$${(num / 1e3).toFixed(2)}K`;
    return `$${num.toFixed(2)}`;
  };

  const formatPercentage = (num: number | null) => {
    if (num === null) return 'N/A';
    return `${num.toFixed(2)}%`;
  };

  if (loading) {
    return (
      <div className="min-h-screen gradient-bg flex items-center justify-center">
        <div className="text-center glass-card p-8 rounded-2xl">
          <Activity className="w-12 h-12 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-700 text-lg font-medium">Loading blockchains...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen gradient-bg flex items-center justify-center">
        <div className="text-center glass-card p-8 rounded-2xl">
          <p className="text-red-600 mb-6 text-lg font-medium">Error: {error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-300 shadow-lg"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen gradient-bg">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-12 text-center">
          <h1 className="text-5xl font-bold text-white mb-4 drop-shadow-lg">
            🚀 Crypto Intelligence Dashboard
          </h1>
          <p className="text-white/90 text-xl font-medium drop-shadow">
            Explore blockchain ecosystems and their top assets with real-time data
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {blockchains.map((blockchain) => (
            <Link
              key={blockchain.id}
              href={`/blockchain/${blockchain.id}`}
              className="block group"
            >
              <div className="glass-card rounded-2xl p-6 hover:scale-105 transition-all duration-300 hover:shadow-2xl">
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                      {blockchain.name}
                    </h3>
                    <p className="text-gray-600 text-sm font-medium uppercase tracking-wider">
                      {blockchain.symbol}
                    </p>
                  </div>
                  <div className="text-right">
                    {blockchain.price_change_24h !== null && (
                      <div className={`flex items-center justify-end px-3 py-1 rounded-full text-sm font-semibold ${
                        blockchain.price_change_24h >= 0
                          ? 'bg-green-100 text-green-700'
                          : 'bg-red-100 text-red-700'
                      }`}>
                        {blockchain.price_change_24h >= 0 ? (
                          <TrendingUp className="w-4 h-4 mr-1" />
                        ) : (
                          <TrendingDown className="w-4 h-4 mr-1" />
                        )}
                        {formatPercentage(blockchain.price_change_24h)}
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between items-center p-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
                    <span className="text-gray-700 font-medium">Market Cap:</span>
                    <span className="text-gray-900 font-bold text-lg">
                      {formatNumber(blockchain.market_cap)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg">
                    <span className="text-gray-700 font-medium">24h Volume:</span>
                    <span className="text-gray-900 font-bold text-lg">
                      {formatNumber(blockchain.volume_24h)}
                    </span>
                  </div>
                </div>

                <div className="mt-6 pt-4 border-t border-gray-200">
                  <div className="flex items-center justify-center text-blue-600 font-semibold group-hover:text-blue-700 transition-colors">
                    <span>View Assets</span>
                    <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                    </svg>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}
